#!/usr/bin/env python3
"""
测试修改后的累积密度变化率可视化功能
"""

import sys
import numpy as np
from pathlib import Path

# 添加当前目录到路径
sys.path.append('.')

def test_visualization():
    """测试可视化功能"""
    try:
        # 导入修改后的可视化器
        import importlib.util
        spec = importlib.util.spec_from_file_location("ship_domain_visualization", "15.ship_domain_visualization.py")
        vis_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(vis_module)
        ShipDomainVisualizer = vis_module.ShipDomainVisualizer
        
        print("✅ 成功导入 ShipDomainVisualizer")
        
        # 创建可视化器实例
        visualizer = ShipDomainVisualizer(debug=True)
        print("✅ 成功创建可视化器实例")
        
        # 检查必要的数据文件
        density_file = Path("result/probability_density/sector_boundaries_results.pkl")
        ellipse_file = Path("result/ship_domain_fitting/ship_domain_ellipse_params.pkl")
        
        print(f"\n=== 数据文件检查 ===")
        print(f"概率密度结果: {'✅' if density_file.exists() else '❌'} {density_file}")
        print(f"椭圆拟合结果: {'✅' if ellipse_file.exists() else '❌'} {ellipse_file}")
        
        if not density_file.exists():
            print("\n⚠️ 缺少概率密度分析结果")
            print("请先运行修改后的: python 13.probability_density_analysis.py")
            return False
            
        if not ellipse_file.exists():
            print("\n⚠️ 缺少椭圆拟合结果")
            print("请先运行: python 14.ship_domain_fitting.py")
            return False
        
        # 加载数据
        print(f"\n=== 加载数据 ===")
        success = visualizer.load_data()
        
        if not success:
            print("❌ 数据加载失败")
            return False
            
        print("✅ 数据加载成功")
        
        # 检查累积密度变化率数据
        print(f"\n=== 检查累积密度变化率数据 ===")
        
        scenes_with_change_rate = []
        for scene_key, density_data in visualizer.density_results.items():
            sector_analysis = density_data.get('sector_analysis', {})
            
            sectors_with_data = 0
            for sector_name, sector_data in sector_analysis.items():
                if (sector_data is not None and 
                    sector_data.get('boundary_info') is not None and
                    'cumulative_densities' in sector_data['boundary_info']):
                    
                    cumulative_densities = sector_data['boundary_info']['cumulative_densities']
                    if cumulative_densities and len(cumulative_densities) > 1:
                        # 检查是否有变化率数据
                        has_change_rates = any(cd.get('density_change_rate', 0) > 0 
                                             for cd in cumulative_densities)
                        if has_change_rates:
                            sectors_with_data += 1
            
            if sectors_with_data > 0:
                scenes_with_change_rate.append((scene_key, sectors_with_data))
                print(f"  {scene_key}: {sectors_with_data} 个扇区有累积密度变化率数据")
        
        if not scenes_with_change_rate:
            print("❌ 没有找到累积密度变化率数据")
            print("请确保使用修改后的 13.probability_density_analysis.py 重新生成数据")
            return False
        
        print(f"✅ 找到 {len(scenes_with_change_rate)} 个场景包含累积密度变化率数据")
        
        # 测试步骤3的可视化
        test_scene = scenes_with_change_rate[0][0]  # 使用第一个有数据的场景
        print(f"\n=== 测试步骤3可视化: {test_scene} ===")
        
        try:
            success = visualizer.create_single_visualization(test_scene, step=3)
            if success:
                print("✅ 步骤3累积密度变化率可视化生成成功")
                print("📁 输出目录: vis/single_visualization/")
                
                # 检查生成的文件
                vis_dir = Path("vis/single_visualization")
                if vis_dir.exists():
                    generated_files = list(vis_dir.glob("*cumulative_analysis.png"))
                    print(f"📊 生成了 {len(generated_files)} 个扇区分析图")
                    
                    step3_files = list(vis_dir.glob("*step3.png"))
                    if step3_files:
                        print(f"📊 生成了步骤3综合图: {step3_files[0].name}")
                
                return True
            else:
                print("❌ 步骤3可视化生成失败")
                return False
                
        except Exception as e:
            print(f"❌ 步骤3可视化测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🎨 累积密度变化率可视化功能测试")
    print("=" * 50)
    
    success = test_visualization()
    
    if success:
        print("\n🎉 测试成功！")
        print("\n📊 新功能说明:")
        print("1. 步骤3现在显示累积密度变化率随距离变化的曲线")
        print("2. 红色点标记密度变化率最大的边界点")
        print("3. 每个扇区生成包含累积密度和变化率的双图分析")
        print("4. 变化率图显示了密度变化的趋势，帮助识别船舶领域边界")
    else:
        print("\n❌ 测试失败")
        print("\n🔧 解决方案:")
        print("1. 确保先运行修改后的 13.probability_density_analysis.py")
        print("2. 确保运行 14.ship_domain_fitting.py")
        print("3. 检查数据文件是否正确生成")
    
    print("\n💡 使用提示:")
    print("- 累积密度变化率反映了随着距离增加，船舶密度的变化趋势")
    print("- 变化率最大的点通常对应船舶领域的边界")
    print("- 新的可视化方法更准确地反映了累积密度分析的核心思想")

if __name__ == '__main__':
    main()
